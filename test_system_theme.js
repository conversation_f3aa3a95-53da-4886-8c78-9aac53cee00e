// Test script to verify system theme detection in the browser
// Run this in the browser console to test the prefers-color-scheme detection

console.log("=== System Theme Detection Test ===");

// Test 1: Check if matchMedia is available
if (window.matchMedia) {
    console.log("✅ window.matchMedia is available");
    
    // Test 2: Check prefers-color-scheme: dark
    const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    console.log("Dark mode query result:", darkModeQuery);
    console.log("Prefers dark mode:", darkModeQuery.matches);
    
    // Test 3: Check prefers-color-scheme: light
    const lightModeQuery = window.matchMedia('(prefers-color-scheme: light)');
    console.log("Light mode query result:", lightModeQuery);
    console.log("Prefers light mode:", lightModeQuery.matches);
    
    // Test 4: Add listener for changes
    darkModeQuery.addEventListener('change', (e) => {
        console.log("Theme preference changed! Dark mode:", e.matches);
    });
    
    console.log("✅ Theme detection test completed");
    console.log("Current system preference:", darkModeQuery.matches ? "Dark" : "Light");
    
} else {
    console.log("❌ window.matchMedia is not available");
}

// Test 5: Instructions for manual testing
console.log("\n=== Manual Testing Instructions ===");
console.log("1. Open your system preferences/settings");
console.log("2. Change between light and dark mode");
console.log("3. Check if the console logs show the change");
console.log("4. Refresh the Warda app and see if it picks up the correct theme");
