use egui::Context;

#[cfg(target_arch = "wasm32")]
use js_sys::Date;

#[cfg(target_arch = "wasm32")]
use web_sys;

/// Theme preference
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, serde::Deserialize, serde::Serialize)]
pub enum ThemePreference {
    Light,
    Dark,
    Frappe,
    Latte,
    Macchiato,
    Mocha,
    System, // Follow system theme
}

impl Default for ThemePreference {
    fn default() -> Self {
        ThemePreference::System
    }
}

impl ThemePreference {
    /// Get the display name for the theme preference
    pub fn display_name(&self) -> &'static str {
        match self {
            ThemePreference::Light => "Light",
            ThemePreference::Dark => "Dark",
            ThemePreference::Frappe => "Frappé",
            ThemePreference::Latte => "Latte",
            ThemePreference::Macchiato => "Macchiato",
            ThemePreference::Mocha => "Mocha",
            ThemePreference::System => "System",
        }
    }

    /// Get all available theme preferences in a logical order
    pub fn all() -> &'static [ThemePreference] {
        &[
            ThemePreference::System,
            ThemePreference::Light,
            ThemePreference::Dark,
            ThemePreference::Latte,
            ThemePreference::Frappe,
            ThemePreference::Macchiato,
            ThemePreference::Mocha,
        ]
    }
}

/// Theme manager for handling theme preferences and system detection
#[derive(serde::Deserialize, serde::Serialize)]
pub struct ThemeManager {
    pub theme_preference: ThemePreference,

    // Theme application tracking (not persisted)
    #[serde(skip)]
    pub last_applied_theme: Option<ThemePreference>,

    // System theme detection caching (not persisted)
    #[serde(skip)]
    pub cached_system_dark_mode: Option<bool>,
    #[serde(skip)]
    pub last_system_check: Option<f64>, // Use f64 timestamp for cross-platform compatibility
}

impl Default for ThemeManager {
    fn default() -> Self {
        Self {
            theme_preference: ThemePreference::default(),
            last_applied_theme: None,
            cached_system_dark_mode: None,
            last_system_check: None,
        }
    }
}

impl ThemeManager {
    pub fn new(theme_preference: ThemePreference) -> Self {
        Self {
            theme_preference,
            last_applied_theme: None,
            cached_system_dark_mode: None,
            last_system_check: None,
        }
    }

    /// Detect if the system is using dark mode (with caching)
    pub fn is_system_dark_mode(&mut self) -> bool {
        let now = Self::get_current_time();

        // Check cache first - only refresh every 5 seconds
        if let (Some(cached), Some(last_check)) = (self.cached_system_dark_mode, self.last_system_check) {
            if (now - last_check) < 5000.0 { // 5 seconds in milliseconds
                return cached;
            }
        }

        // Perform actual system detection
        let is_dark = Self::detect_system_dark_mode();

        // Update cache
        self.cached_system_dark_mode = Some(is_dark);
        self.last_system_check = Some(now);

        is_dark
    }

    /// Get current time in milliseconds (cross-platform)
    fn get_current_time() -> f64 {
        #[cfg(target_arch = "wasm32")]
        {
            Date::now()
        }
        #[cfg(not(target_arch = "wasm32"))]
        {
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_millis() as f64
        }
    }

    /// Actually detect if the system is using dark mode
    fn detect_system_dark_mode() -> bool {
        #[cfg(target_arch = "wasm32")]
        {
            // Use browser's prefers-color-scheme media query to detect system theme
            let window = web_sys::window();
            if let Some(window) = window {
                let media_query_result = window.match_media("(prefers-color-scheme: dark)");
                match media_query_result {
                    Ok(Some(media_query_list)) => {
                        let matches = media_query_list.matches();
                        log::debug!("Browser system theme detection: prefers-color-scheme dark = {}", matches);
                        matches
                    }
                    Ok(None) => {
                        log::debug!("match_media returned None (assuming light)");
                        false
                    }
                    Err(e) => {
                        log::debug!("Failed to query prefers-color-scheme (assuming light): {:?}", e);
                        false // Default to light if detection fails
                    }
                }
            } else {
                log::debug!("No window object available (assuming light)");
                false
            }
        }

        #[cfg(target_os = "macos")]
        {
            use std::process::Command;

            // Use macOS defaults command to check the system appearance
            let output = Command::new("defaults")
                .args(&["read", "-g", "AppleInterfaceStyle"])
                .output();

            match output {
                Ok(output) => {
                    let result = String::from_utf8_lossy(&output.stdout);
                    let is_dark = result.trim() == "Dark";
                    log::debug!("System theme detection: AppleInterfaceStyle = '{}', is_dark = {}", result.trim(), is_dark);
                    is_dark
                }
                Err(e) => {
                    log::debug!("Failed to detect system theme (assuming light): {}", e);
                    false // Default to light if detection fails
                }
            }
        }

        #[cfg(all(not(target_arch = "wasm32"), not(target_os = "macos")))]
        {
            log::debug!("System theme detection not implemented for this platform, defaulting to light");
            false // Default to light for other platforms
        }
    }

    /// Apply the current theme preference to the UI if it has changed
    pub fn apply_theme_if_needed(&mut self, ctx: &Context) {
        // Check if the current visuals match our preference
        let current_is_dark = ctx.style().visuals.dark_mode;
        let should_be_dark = match self.theme_preference {
            ThemePreference::Dark => true,
            ThemePreference::Macchiato => true,
            ThemePreference::Mocha => true,
            ThemePreference::Frappe => true,
            ThemePreference::Latte => false,
            ThemePreference::Light => false,
            ThemePreference::System => self.is_system_dark_mode(),
        };

        if current_is_dark != should_be_dark || self.last_applied_theme != Some(self.theme_preference) {
            match self.theme_preference {
                ThemePreference::Light => {
                    ctx.set_visuals(egui::Visuals::light());
                    log::debug!("Applied light theme (was dark: {})", current_is_dark);
                }
                ThemePreference::Dark => {
                    ctx.set_visuals(egui::Visuals::dark());
                    log::debug!("Applied dark theme (was dark: {})", current_is_dark);
                }
                ThemePreference::Macchiato => {
                    catppuccin_egui::set_theme(ctx, catppuccin_egui::MACCHIATO);
                }
                ThemePreference::Mocha => {
                    catppuccin_egui::set_theme(ctx, catppuccin_egui::MOCHA);
                }
                ThemePreference::Frappe => {
                    catppuccin_egui::set_theme(ctx, catppuccin_egui::FRAPPE);
                }
                ThemePreference::Latte => {
                    catppuccin_egui::set_theme(ctx, catppuccin_egui::LATTE);
                }
                ThemePreference::System => {
                    if should_be_dark {
                        ctx.set_visuals(egui::Visuals::dark());
                        log::debug!("Applied system theme (dark, was dark: {})", current_is_dark);
                    } else {
                        ctx.set_visuals(egui::Visuals::light());
                        log::debug!("Applied system theme (light, was dark: {})", current_is_dark);
                    }
                }
            }
            self.last_applied_theme = Some(self.theme_preference);
        }
    }

    /// Apply the current theme preference to the UI (force apply)
    pub fn apply_theme(&mut self, ctx: &Context) {
        self.last_applied_theme = None; // Force reapplication
        self.apply_theme_if_needed(ctx);
    }

    /// Initialize theme on app startup
    pub fn initialize_theme(&mut self, ctx: &Context) {
        log::info!("Applying stored theme preference: {:?}", self.theme_preference);
        match self.theme_preference {
            ThemePreference::Light => {
                ctx.set_visuals(egui::Visuals::light());
                log::info!("Applied light theme during initialization");
            }
            ThemePreference::Dark => {
                ctx.set_visuals(egui::Visuals::dark());
                log::info!("Applied dark theme during initialization");
            }
            ThemePreference::Macchiato => {
                catppuccin_egui::set_theme(ctx, catppuccin_egui::MACCHIATO);
            }
            ThemePreference::Mocha => {
                catppuccin_egui::set_theme(ctx, catppuccin_egui::MOCHA);
            }
            ThemePreference::Frappe => {
                catppuccin_egui::set_theme(ctx, catppuccin_egui::FRAPPE);
            }
            ThemePreference::Latte => {
                catppuccin_egui::set_theme(ctx, catppuccin_egui::LATTE);
            }
            ThemePreference::System => {
                let is_dark = self.is_system_dark_mode();
                if is_dark {
                    ctx.set_visuals(egui::Visuals::dark());
                    log::info!("Applied system theme (dark) during initialization");
                } else {
                    ctx.set_visuals(egui::Visuals::light());
                    log::info!("Applied system theme (light) during initialization");
                }
            }
        }
        self.last_applied_theme = Some(self.theme_preference);
    }

    /// Set theme preference and apply it
    pub fn set_theme_preference(&mut self, preference: ThemePreference, ctx: &Context) {
        self.theme_preference = preference;
        self.apply_theme(ctx);
        log::debug!("Theme preference changed to: {:?}", self.theme_preference);
    }
}