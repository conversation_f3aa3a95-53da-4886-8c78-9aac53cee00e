pub mod counter;
pub mod text_editor;
pub mod data_list;
pub mod map;

pub use counter::show_counter_view;
pub use text_editor::show_text_editor_view;
pub use data_list::show_data_list_view;
pub use map::show_map_view;

#[cfg(target_arch = "wasm32")]
use web_sys;

/// Available tabs/views
#[derive(<PERSON>lone, Copy, PartialEq, serde::Deserialize, serde::Serialize)]
pub enum Tab {
    Map,
    Counter,
    TextEditor,
    DataList,
}

impl Default for Tab {
    fn default() -> Self {
        Tab::Counter
    }
}

impl Tab {
    pub fn name(&self) -> &'static str {
        match self {
            Tab::Map => "Map",
            Tab::Counter => "Counter",
            Tab::TextEditor => "Text Editor",
            Tab::DataList => "Data List",
        }
    }
}